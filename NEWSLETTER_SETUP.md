# Newsletter Subscription System

This project includes a complete newsletter subscription system with email confirmation and unsubscribe functionality.

## Features

- ✅ Newsletter subscription form with validation
- ✅ Email confirmation with welcome message
- ✅ Unsubscribe functionality with unique tokens
- ✅ Internationalization support (PT, EN, FR)
- ✅ Toast notifications for user feedback
- ✅ Server-side validation and error handling
- ✅ Sanity CMS integration for subscriber management

## Setup Instructions

### 1. Environment Variables

Copy `.env.example` to `.env.local` and configure the following variables:

```bash
# Sanity Configuration (already configured)
NEXT_PUBLIC_SANITY_PROJECT_ID=your_sanity_project_id
NEXT_PUBLIC_SANITY_DATASET=production
NEXT_PUBLIC_SANITY_API_VERSION=2024-08-08
SANITY_API_READ_TOKEN=your_sanity_read_token

# Base URL for the application
NEXT_PUBLIC_BASE_URL=http://localhost:3000

# Email Configuration (SMTP)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
SMTP_FROM=<EMAIL>
```

### 2. Email Configuration

For Gmail SMTP:
1. Enable 2-factor authentication on your Google account
2. Generate an App Password: https://myaccount.google.com/apppasswords
3. Use the App Password as `SMTP_PASS`

For other email providers, adjust the SMTP settings accordingly.

### 3. Sanity Schema

The subscriber schema has been updated with an `unsubscribeToken` field. If you need to update your Sanity dataset, run:

```bash
npm run sanity:deploy
```

## Components

### NewsletterForm
- Location: `src/app/[locale]/(site)/containers/NewsletterForm.tsx`
- Features: Form validation, loading states, error handling
- Uses: react-hook-form, zod validation, sonner for toasts

### Server Actions
- Location: `src/app/[locale]/(site)/actions/newsletter.ts`
- Functions: `subscribeToNewsletter`, `unsubscribeFromNewsletter`
- Features: Email validation, duplicate checking, token generation

### Email Service
- Location: `src/lib/email/newsletter.ts`
- Features: HTML email templates, unsubscribe links
- Uses: Nodemailer for email sending

### Unsubscribe Page
- Location: `src/app/[locale]/(site)/unsubscribe/page.tsx`
- Features: Token validation, success/error states
- Supports: All configured locales

## Usage

### Subscribe to Newsletter
1. User enters email in the newsletter form
2. Form validates the email client-side
3. Server action validates and saves to Sanity
4. Confirmation email is sent with unsubscribe link
5. Success toast is shown to user

### Unsubscribe Process
1. User clicks unsubscribe link in email
2. Token is validated against Sanity database
3. Subscriber status is updated to "unsubscribed"
4. Confirmation page is shown

## Translations

Newsletter-related translations are available in:
- `messages/pt.json` (Portuguese)
- `messages/en.json` (English)
- `messages/fr.json` (French)

## Testing

To test the newsletter system:

1. Set up environment variables
2. Start the development server: `npm run dev`
3. Navigate to the footer newsletter form
4. Enter a valid email address
5. Check your email for the confirmation message
6. Test the unsubscribe link

## Troubleshooting

### Email not sending
- Check SMTP credentials in environment variables
- Verify firewall/network settings
- Check email provider's SMTP settings

### Unsubscribe link not working
- Verify `NEXT_PUBLIC_BASE_URL` is set correctly
- Check that the token exists in Sanity
- Ensure the subscriber document has the `unsubscribeToken` field

### Form validation errors
- Check that all required translations are present
- Verify zod schema matches form fields
- Check browser console for JavaScript errors
