import { Suspense } from "react";
import { notFound } from "next/navigation";
import { unsubscribeFromNewsletter } from "../actions/newsletter";
import { Button } from "@/components/ui/button";
import { Link } from "@/i18n/navigation";
import { getTranslations } from "next-intl/server";
import { Locale } from "@/i18n/routing";

interface UnsubscribePageProps {
  params: Promise<{ locale: Locale }>;
  searchParams: Promise<{ token?: string }>;
}

async function UnsubscribeContent({ 
  token, 
  locale 
}: { 
  token: string; 
  locale: Locale;
}) {
  const t = await getTranslations({ locale, namespace: "Newsletter" });
  
  if (!token) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-primary to-primary-foreground">
        <div className="max-w-md w-full mx-4">
          <div className="bg-white rounded-lg shadow-xl p-8 text-center">
            <div className="w-16 h-16 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center">
              <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </div>
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              {t("unsubscribe.invalidToken")}
            </h1>
            <p className="text-gray-600 mb-6">
              {t("unsubscribe.invalidTokenDescription")}
            </p>
            <Link href="/">
              <Button className="w-full">
                {t("unsubscribe.backToHome")}
              </Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  const result = await unsubscribeFromNewsletter(token);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-primary to-primary-foreground">
      <div className="max-w-md w-full mx-4">
        <div className="bg-white rounded-lg shadow-xl p-8 text-center">
          {result.success ? (
            <>
              <div className="w-16 h-16 mx-auto mb-4 bg-green-100 rounded-full flex items-center justify-center">
                <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h1 className="text-2xl font-bold text-gray-900 mb-4">
                {t("unsubscribe.success")}
              </h1>
              <p className="text-gray-600 mb-6">
                {t("unsubscribe.successDescription")}
              </p>
            </>
          ) : (
            <>
              <div className="w-16 h-16 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center">
                <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </div>
              <h1 className="text-2xl font-bold text-gray-900 mb-4">
                {t("unsubscribe.error")}
              </h1>
              <p className="text-gray-600 mb-6">
                {result.error || t("unsubscribe.errorDescription")}
              </p>
            </>
          )}
          
          <Link href="/">
            <Button className="w-full">
              {t("unsubscribe.backToHome")}
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
}

export default async function UnsubscribePage({ 
  params, 
  searchParams 
}: UnsubscribePageProps) {
  const { locale } = await params;
  const { token } = await searchParams;

  if (!token) {
    notFound();
  }

  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    }>
      <UnsubscribeContent token={token} locale={locale} />
    </Suspense>
  );
}
