"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Link } from "@/i18n/navigation";
import { socialsItems } from "./SocialButtons";
import { useTranslations } from "next-intl";

const NewsletterForm = () => {
  const t = useTranslations("Footer");

  return (
    <div>
      <h5 className="mb-2">{t("newsletter.title")}</h5>
      <form className="flex flex-col items-start gap-4">
        <Input
          type="email"
          placeholder={t("newsletter.emailPlaceholder")}
          className="bg-primary-foreground focus:border-background max-w-sm rounded-none border-transparent [box-shadow:inset_0_0_10px_5px_rgba(0,0,0,0.5)]"
        />
        <span className="flex w-full flex-wrap items-center justify-between gap-4">
          <Button
            variant="outline"
            className="border-background text-background"
          >
            {t("newsletter.submit")}
          </Button>
          <span className="hidden items-center gap-4 sm:flex">
            {socialsItems.map(({ href, Icon }, index) => {
              return (
                <Link key={index} href={href}>
                  <Icon
                    className="size-7"
                    strokeWidth={1.25}
                    innerPathClassName="fill-primary-foreground"
                  />
                </Link>
              );
            })}
          </span>
        </span>
      </form>
    </div>
  );
};

export default NewsletterForm;
