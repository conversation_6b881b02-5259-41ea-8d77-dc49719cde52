import nodemailer from "nodemailer";

// Create transporter
const createTransporter = () => {
  return nodemailer.createTransport({
    host: process.env.SMTP_HOST,
    port: parseInt(process.env.SMTP_PORT || "587"),
    secure: process.env.SMTP_SECURE === "true", // true for 465, false for other ports
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS,
    },
  });
};

export async function sendConfirmationEmail(
  email: string,
  unsubscribeToken: string,
) {
  try {
    const transporter = createTransporter();

    // Get the base URL for unsubscribe link
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:3000";
    const unsubscribeUrl = `${baseUrl}/unsubscribe?token=${unsubscribeToken}`;

    const mailOptions = {
      from: {
        name: "Pedro Yaba",
        address: process.env.SMTP_FROM || process.env.SMTP_USER || "",
      },
      to: email,
      subject: "Bem-vindo à Newsletter do Pedro <PERSON>",
      html: `
        <!DOCTYPE html>
        <html lang="pt">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Bem-vindo à Newsletter</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              line-height: 1.6;
              color: #333;
              max-width: 600px;
              margin: 0 auto;
              padding: 20px;
            }
            .header {
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              color: white;
              padding: 30px;
              text-align: center;
              border-radius: 10px 10px 0 0;
            }
            .content {
              background: #f9f9f9;
              padding: 30px;
              border-radius: 0 0 10px 10px;
            }
            .footer {
              text-align: center;
              margin-top: 30px;
              padding-top: 20px;
              border-top: 1px solid #ddd;
              font-size: 12px;
              color: #666;
            }
            .unsubscribe-link {
              color: #666;
              text-decoration: none;
            }
            .unsubscribe-link:hover {
              text-decoration: underline;
            }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>Bem-vindo à Newsletter!</h1>
            <p>Obrigado por se inscrever na newsletter do Pedro Yaba</p>
          </div>
          
          <div class="content">
            <h2>Olá!</h2>
            <p>Obrigado por se inscrever na nossa newsletter. Você receberá atualizações regulares sobre:</p>
            
            <ul>
              <li>Novos projetos e trabalhos</li>
              <li>Insights sobre arte e design</li>
              <li>Eventos e exposições</li>
              <li>Conteúdo exclusivo para assinantes</li>
            </ul>
            
            <p>Estamos animados para compartilhar nossa jornada criativa com você!</p>
            
            <p>Atenciosamente,<br>
            <strong>Pedro Yaba</strong></p>
          </div>
          
          <div class="footer">
            <p>
              Se você não deseja mais receber estes emails, pode 
              <a href="${unsubscribeUrl}" class="unsubscribe-link">cancelar sua inscrição aqui</a>.
            </p>
            <p>Pedro Yaba | Luanda, Angola</p>
          </div>
        </body>
        </html>
      `,
      text: `
        Bem-vindo à Newsletter do Pedro Yaba!
        
        Olá!
        
        Obrigado por se inscrever na nossa newsletter. Você receberá atualizações regulares sobre:
        
        - Novos projetos e trabalhos
        - Insights sobre arte e design
        - Eventos e exposições
        - Conteúdo exclusivo para assinantes
        
        Estamos animados para compartilhar nossa jornada criativa com você!
        
        Atenciosamente,
        Pedro Yaba
        
        ---
        
        Se você não deseja mais receber estes emails, pode cancelar sua inscrição aqui: ${unsubscribeUrl}
        
        Pedro Yaba | Luanda, Angola
      `,
    };

    await transporter.sendMail(mailOptions);
    console.log("Confirmation email sent successfully to:", email);
  } catch (error) {
    console.error("Error sending confirmation email:", error);
    throw error;
  }
}
